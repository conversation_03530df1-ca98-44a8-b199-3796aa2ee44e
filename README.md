# Bank Offers Web Application

A web application that displays credit card offers from various banks in Sri Lanka. Built with React frontend and Node.js backend.

## Project Structure

- `frontend/`: React application built with Vite
- `backend/`: Node.js API server built with Express

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository:
```
git clone https://github.com/your-username/bank-offers.git
cd bank-offers
```

2. Install dependencies for both frontend and backend:
```
# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

### Running the Application

1. Start the backend server:
```
cd backend
npm run dev
```

2. Start the frontend development server:
```
cd frontend
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173` to view the application.

## Features

- View credit card offers from various banks in Sri Lanka
- Compare different credit card features and benefits
- Detailed information about each credit card offer

## Technologies Used

### Frontend
- React
- React Router
- Axios
- Vite

### Backend
- Node.js
- Express
- MongoDB (with <PERSON><PERSON><PERSON>)
- CORS

## License

This project is licensed under the MIT License.
