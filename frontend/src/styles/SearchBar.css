.search-bar {
	/* Removed margin-bottom as it's now controlled by the parent container */
}

.search-input-container {
	position: relative;
	width: 100%;
}

.search-input {
	width: 100%;
	padding: 12px 40px 12px 15px;
	border: 1px solid #e0e0e0;
	border-radius: 4px;
	font-size: 1rem;
	outline: none;
	transition: all 0.3s ease;
	box-sizing: border-box;
	height: 44px; /* Fixed height to match react-select components */
	background-color: #fff;
}

.search-input:focus {
	border-color: #dee2e6;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	background-color: #f8f9fa;
}

.clear-button {
	position: absolute;
	right: 12px;
	top: 50%;
	transform: translateY(-53%);
	background: none;
	border: none;
	font-size: 21px;
	font-weight: 600;
	color: #999;
	cursor: pointer;
	padding: 4px;
	line-height: 1;
	z-index: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: color 0.2s ease;
	width: 20px;
	height: 20px;
}

.clear-button:hover {
	color: #666;
	background-color: unset;
}

.clear-button:focus {
	outline: none;
	color: #333;
}

@media (max-width: 768px) {
	.search-input {
		margin-bottom: 0;
	}
}
