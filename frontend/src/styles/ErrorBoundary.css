.error-boundary {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400px;
	padding: 20px;
}

.error-content {
	text-align: center;
	max-width: 500px;
	padding: 40px;
	background-color: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #dee2e6;
}

.error-content h2 {
	color: #dc3545;
	margin-bottom: 16px;
	font-size: 1.5rem;
}

.error-content p {
	color: #6c757d;
	margin-bottom: 24px;
	line-height: 1.5;
}

.retry-button {
	background-color: #007bff;
	color: white;
	border: none;
	padding: 12px 24px;
	border-radius: 6px;
	font-size: 1rem;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.retry-button:hover {
	background-color: #0056b3;
}

.error-details {
	margin-top: 20px;
	text-align: left;
}

.error-details summary {
	cursor: pointer;
	color: #6c757d;
	font-weight: 500;
	margin-bottom: 10px;
}

.error-details pre {
	background-color: #f1f3f4;
	padding: 12px;
	border-radius: 4px;
	font-size: 0.85rem;
	overflow-x: auto;
	color: #dc3545;
}
