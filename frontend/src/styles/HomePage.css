.home-page {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

.header {
	text-align: center;
	margin-bottom: 40px;
}

.header h1 {
	color: #333;
	margin-bottom: 10px;
}

.header p {
	color: #666;
	font-size: 1.1rem;
}

/* New styles for the combined filter and search row */
.filters-and-search-row {
	display: flex;
	flex-wrap: wrap; /* Allow items to wrap to the next line on smaller screens */
	gap: 15px; /* Space between items */
	margin-bottom: 30px;
	align-items: stretch; /* Make all items the same height */
}

/* Styling for children of filters-and-search-row */
.filters-and-search-row > .search-bar,
.filters-and-search-row > .bank-filter,
.filters-and-search-row > .category-filter {
	flex: 1 1 250px; /* Grow, shrink, with a base width of 250px */
	min-width: 200px; /* Prevent them from becoming too narrow */
	display: flex;
	flex-direction: column;
}

/* Ensure all filter components have consistent styling */
.filters-and-search-row > .search-bar .search-input-container,
.filters-and-search-row > .bank-filter .filter-container,
.filters-and-search-row > .category-filter .filter-container {
	flex: 1;
}

.offers-container {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	gap: 20px;
}

.loading,
.error,
.no-offers {
	text-align: center;
	padding: 40px;
	font-size: 1.2rem;
	color: #666;
}

.error {
	color: #dc3545;
}

/* Load more button */
.load-more-container {
	grid-column: 1 / -1; /* Span all columns */
	display: flex;
	justify-content: center;
	margin-top: 30px;
	margin-bottom: 20px;
}

.load-more-btn {
	background-color: #f8f9fa;
	color: #495057;
	border: 1px solid #e0e0e0;
	padding: 14px 28px;
	border-radius: 8px;
	font-size: 1rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 180px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.load-more-btn:hover:not(:disabled) {
	background-color: #e9ecef;
	border-color: #dee2e6;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	color: #333;
}

.load-more-btn:disabled {
	background-color: #f8f9fa;
	color: #6c757d;
	border-color: #e0e0e0;
	cursor: not-allowed;
	transform: none;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.load-more-btn:active:not(:disabled) {
	transform: translateY(-1px);
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

/* Custom loading spinner for load more button */
.load-more-spinner {
	width: 18px;
	height: 18px;
	border: 2px solid #e0e0e0;
	border-top: 2px solid #6c757d;
	border-radius: 50%;
	animation: load-more-spin 1s linear infinite;
}

@keyframes load-more-spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* Loading overlay improvements */
.loading-overlay {
	grid-column: 1 / -1;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 60px 20px;
	background-color: #f8f9fa;
	border-radius: 8px;
	border: 2px dashed #dee2e6;
}

@media (max-width: 768px) {
	.filters-and-search-row {
		flex-direction: column;
		gap: 15px;
	}

	.filters-and-search-row > .search-bar,
	.filters-and-search-row > .bank-filter,
	.filters-and-search-row > .category-filter {
		flex: 1 1 auto;
		min-width: unset;
	}

	.offers-container {
		grid-template-columns: 1fr;
	}
}
