.credit-card-offer {
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	margin-bottom: 20px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	transition: transform 0.3s ease;
	position: relative;
}

.credit-card-offer:hover {
	transform: translateY(-5px);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.offer-discount {
	position: absolute;
	top: 0;
	right: 0;
	background-color: #dc3545;
	color: white;
	padding: 8px 12px;
	font-weight: bold;
	font-size: 1rem;
	border-radius: 0 8px 0 8px;
}

.card-header {
	background-color: #f8f9fa;
	padding: 15px;
	border-bottom: 1px solid #e0e0e0;
	padding-right: 80px; /* Make room for the discount tag */
	display: flex;
	align-items: center;
	gap: 15px;
}

.offer-image-container {
	position: relative;
	flex-shrink: 0;
	width: 80px;
	height: 80px;
	border-radius: 8px;
	overflow: hidden;
	background-color: #e9ecef;
}

.offer-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: opacity 0.3s ease;
}

.offer-image.loading {
	opacity: 0;
}

.offer-image.loaded {
	opacity: 1;
}

.image-placeholder {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
}

.image-loading-spinner {
	width: 20px;
	height: 20px;
	border: 2px solid #e9ecef;
	border-top: 2px solid #6c757d;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.card-header-content {
	flex: 1;
	min-width: 0; /* Allow text to truncate if needed */
}

.card-header-content h3 {
	margin: 0;
	color: #333;
	font-size: 1.2rem;
}

.bank-name {
	display: inline-block;
	margin-top: 5px;
	color: #666;
	font-size: 0.9rem;
	background-color: #e9ecef;
	padding: 3px 8px;
	border-radius: 4px;
}

.card-body {
	padding: 15px;
}

.offer-description {
	margin: 8px 0 15px;
	font-size: 0.95rem;
	line-height: 1.4;
	color: #333;
}

.offer-category {
	margin-bottom: 15px;
}

.offer-validity {
	margin-top: 15px;
	padding-top: 15px;
	border-top: 1px dashed #e0e0e0;
	font-size: 0.9rem;
	color: #666;
}

.card-details h4 {
	margin: 0 0 10px;
	font-size: 1rem;
	color: #333;
}

.categories-list {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-bottom: 15px;
}

.category-tag {
	background-color: #e9ecef;
	color: #495057;
	padding: 4px 10px;
	border-radius: 20px;
	font-size: 0.8rem;
	display: inline-block;
}

.card-details ul {
	margin: 0;
	padding-left: 20px;
}

.card-details li {
	margin-bottom: 5px;
	font-size: 0.9rem;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
	.card-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 10px;
		padding-right: 80px; /* Keep space for discount tag */
	}

	.offer-image-container {
		width: 100%;
		height: 120px;
		align-self: stretch;
	}

	.card-header-content {
		width: 100%;
	}
}

@media (max-width: 480px) {
	.offer-image-container {
		height: 100px;
	}

	.card-header {
		padding: 12px;
		padding-right: 70px;
	}

	.offer-discount {
		padding: 6px 10px;
		font-size: 0.9rem;
	}
}
