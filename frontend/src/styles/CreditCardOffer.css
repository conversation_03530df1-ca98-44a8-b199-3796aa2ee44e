.credit-card-offer {
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	margin-bottom: 20px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	transition: transform 0.3s ease;
	position: relative;
}

.credit-card-offer:hover {
	transform: translateY(-5px);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.offer-discount {
	position: absolute;
	top: 0;
	right: 0;
	background-color: #dc3545;
	color: white;
	padding: 8px 12px;
	font-weight: bold;
	font-size: 1rem;
	border-radius: 0 8px 0 8px;
}

.card-header {
	background-color: #f8f9fa;
	padding: 15px;
	border-bottom: 1px solid #e0e0e0;
	padding-right: 80px; /* Make room for the discount tag */
}

.card-header h3 {
	margin: 0;
	color: #333;
	font-size: 1.2rem;
}

.bank-name {
	display: inline-block;
	margin-top: 5px;
	color: #666;
	font-size: 0.9rem;
	background-color: #e9ecef;
	padding: 3px 8px;
	border-radius: 4px;
}

.card-body {
	padding: 15px;
}

.offer-description {
	margin: 8px 0 15px;
	font-size: 0.95rem;
	line-height: 1.4;
	color: #333;
}

.offer-category {
	margin-bottom: 15px;
}

.offer-validity {
	margin-top: 15px;
	padding-top: 15px;
	border-top: 1px dashed #e0e0e0;
	font-size: 0.9rem;
	color: #666;
}

.card-details h4 {
	margin: 0 0 10px;
	font-size: 1rem;
	color: #333;
}

.categories-list {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-bottom: 15px;
}

.category-tag {
	background-color: #e9ecef;
	color: #495057;
	padding: 4px 10px;
	border-radius: 20px;
	font-size: 0.8rem;
	display: inline-block;
}

.card-details ul {
	margin: 0;
	padding-left: 20px;
}

.card-details li {
	margin-bottom: 5px;
	font-size: 0.9rem;
}
