.offer-details-page {
	min-height: 100vh;
	background-color: #f8f9fa;
	padding: 20px;
}

.offer-details-container {
	max-width: 800px;
	margin: 0 auto;
}

.back-btn {
	background: none;
	border: none;
	color: #007bff;
	font-size: 1rem;
	cursor: pointer;
	padding: 10px 0;
	margin-bottom: 20px;
	display: flex;
	align-items: center;
	gap: 5px;
	transition: color 0.3s ease;
}

.back-btn:hover {
	color: #0056b3;
	text-decoration: underline;
}

.loading-container,
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 400px;
	text-align: center;
}

.error-container h2 {
	color: #dc3545;
	margin-bottom: 10px;
}

.error-container p {
	color: #666;
	margin-bottom: 20px;
}

.offer-details-card {
	background: white;
	border-radius: 12px;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	position: relative;
}

.offer-discount-badge {
	position: absolute;
	top: 20px;
	right: 20px;
	background-color: #dc3545;
	color: white;
	padding: 12px 16px;
	font-weight: bold;
	font-size: 1.2rem;
	border-radius: 8px;
	z-index: 10;
}

.offer-image-section {
	position: relative;
	width: 100%;
	height: 300px;
	background-color: #e9ecef;
	overflow: hidden;
}

.offer-detail-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: opacity 0.3s ease;
}

.offer-detail-image.loading {
	opacity: 0;
}

.offer-detail-image.loaded {
	opacity: 1;
}

.image-placeholder-large {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
}

.image-loading-spinner {
	width: 40px;
	height: 40px;
	border: 3px solid #e9ecef;
	border-top: 3px solid #6c757d;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.offer-content {
	padding: 30px;
}

.offer-header {
	margin-bottom: 20px;
}

.merchant-name {
	font-size: 2rem;
	font-weight: bold;
	color: #333;
	margin: 0 0 10px 0;
}

.bank-badge {
	display: inline-block;
	background-color: #007bff;
	color: white;
	padding: 6px 12px;
	border-radius: 20px;
	font-size: 0.9rem;
	font-weight: 500;
}

.offer-category-section {
	margin-bottom: 25px;
}

.category-badge {
	background-color: #e9ecef;
	color: #495057;
	padding: 8px 16px;
	border-radius: 25px;
	font-size: 0.9rem;
	display: inline-block;
}

.offer-description-section {
	margin-bottom: 30px;
}

.offer-description-section h3 {
	color: #333;
	margin-bottom: 15px;
	font-size: 1.3rem;
}

.offer-description {
	font-size: 1.1rem;
	line-height: 1.6;
	color: #555;
	margin: 0;
}

.offer-info-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 20px;
	margin-bottom: 30px;
	padding: 20px;
	background-color: #f8f9fa;
	border-radius: 8px;
}

.info-item h4 {
	color: #333;
	margin: 0 0 8px 0;
	font-size: 1rem;
	font-weight: 600;
}

.info-item p {
	color: #666;
	margin: 0;
	font-size: 1.1rem;
	font-weight: 500;
}

.terms-section {
	margin-bottom: 30px;
	padding: 20px;
	background-color: #fff3cd;
	border-left: 4px solid #ffc107;
	border-radius: 4px;
}

.terms-section h3 {
	color: #856404;
	margin-bottom: 15px;
	font-size: 1.2rem;
}

.terms-text {
	color: #856404;
	line-height: 1.5;
	margin: 0;
}

.action-section {
	display: flex;
	gap: 15px;
	flex-wrap: wrap;
}

.contact-bank-btn,
.share-btn {
	padding: 12px 24px;
	border: none;
	border-radius: 6px;
	font-size: 1rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	flex: 1;
	min-width: 150px;
}

.contact-bank-btn {
	background-color: #28a745;
	color: white;
}

.contact-bank-btn:hover {
	background-color: #218838;
	transform: translateY(-2px);
}

.share-btn {
	background-color: #6c757d;
	color: white;
}

.share-btn:hover {
	background-color: #5a6268;
	transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
	.offer-details-page {
		padding: 15px;
	}

	.offer-discount-badge {
		top: 15px;
		right: 15px;
		padding: 10px 14px;
		font-size: 1rem;
	}

	.offer-image-section {
		height: 200px;
	}

	.offer-content {
		padding: 20px;
	}

	.merchant-name {
		font-size: 1.6rem;
	}

	.offer-info-grid {
		grid-template-columns: 1fr;
		gap: 15px;
		padding: 15px;
	}

	.action-section {
		flex-direction: column;
	}

	.contact-bank-btn,
	.share-btn {
		min-width: auto;
	}
}

@media (max-width: 480px) {
	.offer-details-page {
		padding: 10px;
	}

	.offer-content {
		padding: 15px;
	}

	.merchant-name {
		font-size: 1.4rem;
	}

	.offer-description {
		font-size: 1rem;
	}
}
