.loading-spinner-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20px;
}

.loading-spinner {
	display: inline-block;
	position: relative;
}

.spinner-circle {
	border: 3px solid #f3f3f3;
	border-top: 3px solid #007bff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

/* Size variants */
.spinner-small .spinner-circle {
	width: 20px;
	height: 20px;
	border-width: 2px;
}

.spinner-medium .spinner-circle {
	width: 40px;
	height: 40px;
	border-width: 3px;
}

.spinner-large .spinner-circle {
	width: 60px;
	height: 60px;
	border-width: 4px;
}

.loading-message {
	margin-top: 12px;
	color: #6c757d;
	font-size: 0.9rem;
	text-align: center;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
