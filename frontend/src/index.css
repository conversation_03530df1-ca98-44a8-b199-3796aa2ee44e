:root {
	font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
	line-height: 1.5;
	font-weight: 400;

	font-synthesis: none;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

body {
	margin: 0;
	min-width: 320px;
	min-height: 100vh;
	color: #333;
	background-color: #f8f9fa;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin-bottom: 0.5rem;
	font-weight: 500;
	line-height: 1.2;
}

h1 {
	font-size: 2.5rem;
}

h2 {
	font-size: 2rem;
}

h3 {
	font-size: 1.75rem;
}

h4 {
	font-size: 1.5rem;
}

p {
	margin-bottom: 1rem;
}

a {
	color: #007bff;
	text-decoration: none;
}

a:hover {
	color: #0056b3;
	text-decoration: underline;
}

button {
	border-radius: 4px;
	border: 1px solid transparent;
	padding: 0.6em 1.2em;
	font-size: 1em;
	font-weight: 500;
	font-family: inherit;
	background-color: #007bff;
	color: white;
	cursor: pointer;
	transition: background-color 0.25s;
}

button:hover {
	background-color: #0056b3;
}

button:focus,
button:focus-visible {
	outline: 2px solid #0056b3;
}
