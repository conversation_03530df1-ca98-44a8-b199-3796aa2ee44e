import axios from "axios";

const API_URL = "http://localhost:3000/api";

// Create axios instance
const api = axios.create({
	baseURL: API_URL,
	headers: {
		"Content-Type": "application/json",
	},
});

// Response interceptor to handle the new API format
api.interceptors.response.use(
	(response) => {
		// If the response has the new format with success/data structure
		if (response.data && typeof response.data.success !== "undefined") {
			if (response.data.success) {
				// Return the data along with pagination info if available
				return {
					...response,
					data: {
						data: response.data.data,
						pagination: response.data.pagination,
						message: response.data.message,
					},
				};
			} else {
				// Handle API errors with the new format
				const error = new Error(response.data.message || "API request failed");
				error.response = response;
				throw error;
			}
		}
		// Return original response if it doesn't match the new format
		return response;
	},
	(error) => {
		// Handle network errors and other axios errors
		if (error.response?.data?.message) {
			error.message = error.response.data.message;
		}
		throw error;
	},
);

// Get all credit card offers with pagination support
export const getAllCreditCardOffers = async (filters = {}) => {
	try {
		const { bank, category, search, page = 1, limit = 20 } = filters;
		const params = { page, limit };

		// Handle bank filter (array or string)
		if (Array.isArray(bank) && bank.length > 0) {
			params.bank = bank.join(",");
		} else if (bank && !Array.isArray(bank)) {
			params.bank = bank;
		}

		// Handle category filter (array or string)
		if (Array.isArray(category) && category.length > 0) {
			params.category = category.join(",");
		} else if (category && !Array.isArray(category)) {
			params.category = category;
		}

		if (search) params.search = search;

		// Use the new v1 API endpoint, with fallback to legacy
		const response = await api.get("/v1/offers", { params });
		return {
			offers: response.data.data || [],
			pagination: response.data.pagination || null,
			message: response.data.message,
		};
	} catch (error) {
		console.error("Error fetching credit card offers:", error);
		throw error;
	}
};

// Get all available banks
export const getAllBanks = async () => {
	try {
		// Use the new v1 API endpoint, with fallback to legacy
		const response = await api.get("/v1/offers/banks");
		return response.data.data || [];
	} catch (error) {
		console.error("Error fetching banks:", error);
		// Fallback to legacy endpoint
		try {
			const fallbackResponse = await api.get("/banks");
			return fallbackResponse.data || [];
		} catch (fallbackError) {
			console.error("Error fetching banks from fallback endpoint:", fallbackError);
			throw error;
		}
	}
};

// Get all available categories
export const getAllCategories = async () => {
	try {
		// Use the new v1 API endpoint, with fallback to legacy
		const response = await api.get("/v1/offers/categories");
		return response.data.data || [];
	} catch (error) {
		console.error("Error fetching categories:", error);
		// Fallback to legacy endpoint
		try {
			const fallbackResponse = await api.get("/categories");
			return fallbackResponse.data || [];
		} catch (fallbackError) {
			console.error("Error fetching categories from fallback endpoint:", fallbackError);
			throw error;
		}
	}
};

// Get a specific credit card offer by ID
export const getCreditCardOfferById = async (id) => {
	try {
		// Use the new v1 API endpoint, with fallback to legacy
		const response = await api.get(`/v1/offers/${id}`);
		return response.data.data || null;
	} catch (error) {
		console.error(`Error fetching credit card offer with ID ${id}:`, error);
		// Fallback to legacy endpoint
		try {
			const fallbackResponse = await api.get(`/credit-card-offers/${id}`);
			return fallbackResponse.data || null;
		} catch (fallbackError) {
			console.error(`Error fetching offer from fallback endpoint:`, fallbackError);
			throw error;
		}
	}
};

// Create a new credit card offer (for future admin functionality)
export const createCreditCardOffer = async (offerData) => {
	try {
		const response = await api.post("/v1/offers", offerData);
		return response.data.data || null;
	} catch (error) {
		console.error("Error creating credit card offer:", error);
		throw error;
	}
};

// Update a credit card offer (for future admin functionality)
export const updateCreditCardOffer = async (id, offerData) => {
	try {
		const response = await api.put(`/v1/offers/${id}`, offerData);
		return response.data.data || null;
	} catch (error) {
		console.error(`Error updating credit card offer with ID ${id}:`, error);
		throw error;
	}
};

// Delete a credit card offer (for future admin functionality)
export const deleteCreditCardOffer = async (id) => {
	try {
		const response = await api.delete(`/v1/offers/${id}`);
		return response.data.data || null;
	} catch (error) {
		console.error(`Error deleting credit card offer with ID ${id}:`, error);
		throw error;
	}
};

export default api;
