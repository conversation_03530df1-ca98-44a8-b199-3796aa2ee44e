import PropTypes from "prop-types";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import "../styles/CreditCardOffer.css";

const CreditCardOffer = ({ offer }) => {
	const navigate = useNavigate();
	const [imageLoaded, setImageLoaded] = useState(false);
	const [imageError, setImageError] = useState(false);

	const handleImageLoad = () => {
		setImageLoaded(true);
	};

	const handleImageError = () => {
		setImageError(true);
		setImageLoaded(true);
	};

	const handleCardClick = () => {
		navigate(`/offer/${offer._id || offer.id}`);
	};

	return (
		<div className="credit-card-offer" onClick={handleCardClick}>
			<div className="offer-discount">{offer.discount}</div>

			<div className="card-header">
				{offer.imageUrl && !imageError && (
					<div className="offer-image-container">
						<img
							src={offer.imageUrl}
							alt={`${offer.merchant} offer`}
							className={`offer-image ${imageLoaded ? "loaded" : "loading"}`}
							onLoad={handleImageLoad}
							onError={handleImageError}
						/>
						{!imageLoaded && (
							<div className="image-placeholder">
								<div className="image-loading-spinner"></div>
							</div>
						)}
					</div>
				)}
				<div className="card-header-content">
					<h3>{offer.merchant}</h3>
					<span className="bank-name">{offer.bank}</span>
				</div>
			</div>

			<div className="card-body">
				<div className="card-info">
					<p className="offer-description">{offer.description}</p>
					<div className="offer-category">
						<span className="category-tag">{offer.category}</span>
					</div>
				</div>

				<div className="offer-validity">
					<p>
						Valid until: <strong>{offer.validUntil}</strong>
					</p>
				</div>
			</div>
		</div>
	);
};

CreditCardOffer.propTypes = {
	offer: PropTypes.shape({
		id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
		_id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
		bank: PropTypes.string.isRequired,
		merchant: PropTypes.string.isRequired,
		discount: PropTypes.string.isRequired,
		description: PropTypes.string.isRequired,
		validUntil: PropTypes.string.isRequired,
		category: PropTypes.string.isRequired,
		imageUrl: PropTypes.string,
		minimumSpend: PropTypes.number,
		maximumDiscount: PropTypes.number,
		terms: PropTypes.string,
		isActive: PropTypes.bool,
		createdAt: PropTypes.string,
		updatedAt: PropTypes.string,
	}).isRequired,
};

export default CreditCardOffer;
