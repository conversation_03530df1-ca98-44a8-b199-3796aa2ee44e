import PropTypes from "prop-types";
import "../styles/CreditCardOffer.css";

const CreditCardOffer = ({ offer }) => {
	return (
		<div className="credit-card-offer">
			<div className="offer-discount">{offer.discount}</div>

			<div className="card-header">
				<h3>{offer.merchant}</h3>
				<span className="bank-name">{offer.bank}</span>
			</div>

			<div className="card-body">
				<div className="card-info">
					<p className="offer-description">{offer.description}</p>
					<div className="offer-category">
						<span className="category-tag">{offer.category}</span>
					</div>
				</div>

				<div className="offer-validity">
					<p>
						Valid until: <strong>{offer.validUntil}</strong>
					</p>
				</div>
			</div>
		</div>
	);
};

CreditCardOffer.propTypes = {
	offer: PropTypes.shape({
		id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
		_id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
		bank: PropTypes.string.isRequired,
		merchant: PropTypes.string.isRequired,
		discount: PropTypes.string.isRequired,
		description: PropTypes.string.isRequired,
		validUntil: PropTypes.string.isRequired,
		category: PropTypes.string.isRequired,
		imageUrl: PropTypes.string,
		minimumSpend: PropTypes.number,
		maximumDiscount: PropTypes.number,
		terms: PropTypes.string,
		isActive: PropTypes.bool,
		createdAt: PropTypes.string,
		updatedAt: PropTypes.string,
	}).isRequired,
};

export default CreditCardOffer;
