import PropTypes from "prop-types";
import "../styles/LoadingSpinner.css";

const LoadingSpinner = ({ size = "medium", message = "Loading..." }) => {
	const sizeClass = `spinner-${size}`;
	
	return (
		<div className="loading-spinner-container">
			<div className={`loading-spinner ${sizeClass}`}>
				<div className="spinner-circle"></div>
			</div>
			{message && <p className="loading-message">{message}</p>}
		</div>
	);
};

LoadingSpinner.propTypes = {
	size: PropTypes.oneOf(["small", "medium", "large"]),
	message: PropTypes.string,
};

export default LoadingSpinner;
