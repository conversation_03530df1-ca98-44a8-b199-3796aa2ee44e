import Select from "react-select";
import PropTypes from "prop-types";
import "../styles/BankFilter.css";

const BankFilter = ({ banks, selectedBanks, onBankSelect }) => {
	const bankOptions = banks.map((bank) => ({
		value: bank,
		label: bank,
	}));

	const selectedOptions = selectedBanks.map((bank) => ({
		value: bank,
		label: bank,
	}));

	const handleChange = (selected) => {
		if (selected) {
			onBankSelect(selected.map((option) => option.value));
		} else {
			onBankSelect([]);
		}
	};

	return (
		<div className="bank-filter">
			<div className="filter-container">
				<Select
					isMulti={true}
					options={bankOptions}
					value={selectedOptions}
					onChange={handleChange}
					className="bank-select-container"
					classNamePrefix="bank-select"
					placeholder="Select banks..."
				/>
			</div>
		</div>
	);
};

BankFilter.propTypes = {
	banks: PropTypes.arrayOf(PropTypes.string).isRequired,
	selectedBanks: PropTypes.arrayOf(PropTypes.string).isRequired,
	onBankSelect: PropTypes.func.isRequired,
};

export default BankFilter;
