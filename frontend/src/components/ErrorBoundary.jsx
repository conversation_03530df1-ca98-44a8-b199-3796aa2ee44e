import { Component } from "react";
import PropTypes from "prop-types";
import "../styles/ErrorBoundary.css";

class ErrorBoundary extends Component {
	constructor(props) {
		super(props);
		this.state = { hasError: false, error: null };
	}

	static getDerivedStateFromError(error) {
		// Update state so the next render will show the fallback UI
		return { hasError: true, error };
	}

	componentDidCatch(error, errorInfo) {
		// Log the error to console or error reporting service
		console.error("Error caught by ErrorBoundary:", error, errorInfo);
	}

	render() {
		if (this.state.hasError) {
			return (
				<div className="error-boundary">
					<div className="error-content">
						<h2>Oops! Something went wrong</h2>
						<p>We're sorry, but something unexpected happened. Please try refreshing the page.</p>
						<button className="retry-button" onClick={() => window.location.reload()}>
							Refresh Page
						</button>
						{process.env.NODE_ENV === "development" && (
							<details className="error-details">
								<summary>Error Details (Development)</summary>
								<pre>{this.state.error?.toString()}</pre>
							</details>
						)}
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

ErrorBoundary.propTypes = {
	children: PropTypes.node.isRequired,
};

export default ErrorBoundary;
