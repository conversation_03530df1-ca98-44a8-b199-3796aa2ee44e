import { useRef, useState, useEffect } from "react";
import PropTypes from "prop-types";
import "../styles/SearchBar.css";

const SearchBar = ({ value, onChange }) => {
	const inputRef = useRef(null);
	const [isFocused, setIsFocused] = useState(false);

	const handleInputChange = (e) => {
		onChange(e.target.value);
	};

	const handleClear = () => {
		onChange("");
		// Focus the input after clearing
		setTimeout(() => {
			if (inputRef.current) {
				inputRef.current.focus();
			}
		}, 0);
	};

	const handleFocus = () => setIsFocused(true);
	const handleBlur = () => setIsFocused(false);

	// Focus input on mount
	useEffect(() => {
		if (inputRef.current) {
			inputRef.current.focus();
		}
	}, []);

	return (
		<div className="search-bar">
			<div className="search-input-container">
				<input
					ref={inputRef}
					type="text"
					placeholder="Search credit card offers..."
					value={value}
					onChange={handleInputChange}
					onFocus={handleFocus}
					onBlur={handleBlur}
					className="search-input"
				/>
				{value && (
					<button
						type="button"
						className="clear-button"
						onClick={handleClear}
						tabIndex={-1}
						onMouseDown={(e) => e.preventDefault()}
					>
						×
					</button>
				)}
			</div>
		</div>
	);
};

SearchBar.propTypes = {
	value: PropTypes.string.isRequired,
	onChange: PropTypes.func.isRequired,
};

export default SearchBar;
