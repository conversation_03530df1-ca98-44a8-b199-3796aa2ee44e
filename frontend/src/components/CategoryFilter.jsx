import Select from "react-select";
import PropTypes from "prop-types";
import "../styles/CategoryFilter.css";

const CategoryFilter = ({ categories, selectedCategories, onCategorySelect }) => {
	const categoryOptions = categories.map((category) => ({
		value: category,
		label: category,
	}));

	const selectedOptions = selectedCategories.map((category) => ({
		value: category,
		label: category,
	}));

	const handleChange = (selected) => {
		if (selected) {
			onCategorySelect(selected.map((option) => option.value));
		} else {
			onCategorySelect([]);
		}
	};

	return (
		<div className="category-filter">
			<div className="filter-container">
				<Select
					isMulti={true}
					options={categoryOptions}
					value={selectedOptions}
					onChange={handleChange}
					className="category-select-container"
					classNamePrefix="category-select"
					placeholder="Select categories..."
				/>
			</div>
		</div>
	);
};

CategoryFilter.propTypes = {
	categories: PropTypes.arrayOf(PropTypes.string).isRequired,
	selectedCategories: PropTypes.arrayOf(PropTypes.string).isRequired,
	onCategorySelect: PropTypes.func.isRequired,
};

export default CategoryFilter;
