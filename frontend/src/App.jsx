import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import HomePage from "./pages/HomePage";
import ErrorBoundary from "./components/ErrorBoundary";
import "./App.css";

function App() {
	return (
		<ErrorBoundary>
			<Router>
				<div className="app">
					<Routes>
						<Route path="/" element={<HomePage />} />
					</Routes>
				</div>
			</Router>
		</ErrorBoundary>
	);
}

export default App;
