import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { getCreditCardOfferById } from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner";
import "../styles/OfferDetailsPage.css";

const OfferDetailsPage = () => {
	const { id } = useParams();
	const navigate = useNavigate();
	const [offer, setOffer] = useState(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [imageLoaded, setImageLoaded] = useState(false);
	const [imageError, setImageError] = useState(false);

	useEffect(() => {
		const fetchOffer = async () => {
			try {
				setLoading(true);
				setError(null);
				const offerData = await getCreditCardOfferById(id);
				setOffer(offerData);
			} catch (err) {
				setError(err.message || "Failed to fetch offer details. Please try again later.");
			} finally {
				setLoading(false);
			}
		};

		if (id) {
			fetchOffer();
		}
	}, [id]);

	const handleImageLoad = () => {
		setImageLoaded(true);
	};

	const handleImageError = () => {
		setImageError(true);
		setImageLoaded(true);
	};

	const handleBackClick = () => {
		navigate(-1); // Go back to previous page
	};

	if (loading) {
		return (
			<div className="offer-details-page">
				<div className="loading-container">
					<LoadingSpinner size="large" message="Loading offer details..." />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="offer-details-page">
				<div className="error-container">
					<h2>Error</h2>
					<p>{error}</p>
					<button className="back-btn" onClick={handleBackClick}>
						← Back to Offers
					</button>
				</div>
			</div>
		);
	}

	if (!offer) {
		return (
			<div className="offer-details-page">
				<div className="error-container">
					<h2>Offer Not Found</h2>
					<p>The offer you're looking for doesn't exist or has been removed.</p>
					<button className="back-btn" onClick={handleBackClick}>
						← Back to Offers
					</button>
				</div>
			</div>
		);
	}

	return (
		<div className="offer-details-page">
			<div className="offer-details-container">
				<button className="back-btn" onClick={handleBackClick}>
					← Back to Offers
				</button>

				<div className="offer-details-card">
					<div className="offer-discount-badge">{offer.discount}</div>

					{offer.imageUrl && !imageError && (
						<div className="offer-image-section">
							<img
								src={offer.imageUrl}
								alt={`${offer.merchant} offer`}
								className={`offer-detail-image ${imageLoaded ? "loaded" : "loading"}`}
								onLoad={handleImageLoad}
								onError={handleImageError}
							/>
							{!imageLoaded && (
								<div className="image-placeholder-large">
									<div className="image-loading-spinner"></div>
								</div>
							)}
						</div>
					)}

					<div className="offer-content">
						<div className="offer-header">
							<h1 className="merchant-name">{offer.merchant}</h1>
							<span className="bank-badge">{offer.bank}</span>
						</div>

						<div className="offer-category-section">
							<span className="category-badge">{offer.category}</span>
						</div>

						<div className="offer-description-section">
							<h3>Offer Details</h3>
							<p className="offer-description">{offer.description}</p>
						</div>

						<div className="offer-info-grid">
							<div className="info-item">
								<h4>Valid Until</h4>
								<p>{offer.validUntil}</p>
							</div>

							{offer.minimumSpend && (
								<div className="info-item">
									<h4>Minimum Spend</h4>
									<p>Rs. {offer.minimumSpend.toLocaleString()}</p>
								</div>
							)}

							{offer.maximumDiscount && (
								<div className="info-item">
									<h4>Maximum Discount</h4>
									<p>Rs. {offer.maximumDiscount.toLocaleString()}</p>
								</div>
							)}
						</div>

						{offer.terms && (
							<div className="terms-section">
								<h3>Terms & Conditions</h3>
								<p className="terms-text">{offer.terms}</p>
							</div>
						)}

						<div className="action-section">
							<button className="contact-bank-btn">
								Contact {offer.bank}
							</button>
							<button className="share-btn">
								Share Offer
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default OfferDetailsPage;
