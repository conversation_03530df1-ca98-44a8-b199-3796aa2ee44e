const express = require("express");
const {
    getAllOffers,
    getOfferById,
    createOffer,
    updateOffer,
    deleteOffer,
    getAllBanks,
    getAllCategories
} = require("../../controllers/offer.controller");

const router = express.Router();

// GET /api/v1/offers - Get all offers with filtering and pagination
router.get("/", getAllOffers);

// GET /api/v1/offers/banks - Get all available banks
router.get("/banks", getAllBanks);

// GET /api/v1/offers/categories - Get all available categories
router.get("/categories", getAllCategories);

// GET /api/v1/offers/:id - Get specific offer by ID
router.get("/:id", getOfferById);

// POST /api/v1/offers - Create new offer
router.post("/", createOffer);

// PUT /api/v1/offers/:id - Update offer by ID
router.put("/:id", updateOffer);

// DELETE /api/v1/offers/:id - Delete offer by ID (soft delete)
router.delete("/:id", deleteOffer);

module.exports = router;
