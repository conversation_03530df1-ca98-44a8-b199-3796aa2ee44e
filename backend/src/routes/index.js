const express = require("express");
const offerRoutes = require("./v1/offer.routes");

const router = express.Router();

// Health check endpoint
router.get("/health", (_req, res) => {
	res.status(200).json({
		success: true,
		message: "Bank Offers API is running",
		timestamp: new Date().toISOString(),
		version: "1.0.0",
	});
});

// API v1 routes
router.use("/v1/offers", offerRoutes);

// Legacy routes for backward compatibility
router.use("/credit-card-offers", offerRoutes);

// Legacy bank and category routes
router.get("/banks", (req, res) => {
	// Forward to the offer controller's getAllBanks method
	const { getAllBanks } = require("../controllers/offer.controller");
	getAllBanks(req, res);
});

router.get("/categories", (req, res) => {
	// Forward to the offer controller's getAllCategories method
	const { getAllCategories } = require("../controllers/offer.controller");
	getAllCategories(req, res);
});

// 404 handler for API routes
router.use("*", (_req, res) => {
	res.status(404).json({
		success: false,
		message: "API endpoint not found",
		data: null,
	});
});

module.exports = router;
