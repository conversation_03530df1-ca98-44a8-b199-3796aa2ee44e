// Validation middleware placeholder
// This will be used with JO<PERSON> or Zod schemas for request validation

const logger = require("../utils/logger");

/**
 * Generic validation middleware factory
 * @param {Object} schema - Validation schema (JOI/Zod)
 * @param {string} property - Request property to validate ('body', 'params', 'query')
 * @returns {Function} Express middleware function
 */
const validate = (schema, property = "body") => {
    return (req, res, next) => {
        // TODO: Implement validation using JOI or Zod
        // For now, this is a placeholder that allows all requests
        
        logger.debug(`Validation middleware - validating ${property} (not implemented yet)`);
        next();
    };
};

/**
 * Middleware to validate MongoDB ObjectId
 * @param {string} paramName - Name of the parameter to validate
 * @returns {Function} Express middleware function
 */
const validateObjectId = (paramName = "id") => {
    return (req, res, next) => {
        const id = req.params[paramName];
        
        // Basic ObjectId format validation (24 hex characters)
        const objectIdRegex = /^[0-9a-fA-F]{24}$/;
        
        if (!id || !objectIdRegex.test(id)) {
            return res.status(400).json({
                success: false,
                message: `Invalid ${paramName} format`,
                data: null
            });
        }
        
        next();
    };
};

module.exports = {
    validate,
    validateObjectId
};
