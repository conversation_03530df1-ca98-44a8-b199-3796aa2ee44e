const express = require("express");
const cors = require("cors");
const logger = require("./utils/logger");
const routes = require("./routes");

const createApp = () => {
	const app = express();

	// Trust proxy for accurate IP addresses
	app.set("trust proxy", 1);

	// CORS configuration
	const corsOptions = {
		origin: process.env.CORS_ORIGIN || "http://localhost:5173",
		credentials: true,
		optionsSuccessStatus: 200,
		methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
		allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
	};

	app.use(cors(corsOptions));

	// Body parsing middleware
	app.use(express.json({ limit: "10mb" }));
	app.use(express.urlencoded({ extended: true, limit: "10mb" }));

	// Request logging middleware
	app.use((req, res, next) => {
		const start = Date.now();

		res.on("finish", () => {
			const duration = Date.now() - start;
			const logLevel = res.statusCode >= 400 ? "warn" : "info";

			logger[logLevel](`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
		});

		next();
	});

	// Root endpoint
	app.get("/", (req, res) => {
		res.status(200).json({
			success: true,
			message: "Bank Offers API is running",
			version: "1.0.0",
			timestamp: new Date().toISOString(),
			endpoints: {
				health: "/api/health",
				offers: "/api/v1/offers",
				banks: "/api/v1/offers/banks",
				categories: "/api/v1/offers/categories",
			},
		});
	});

	// API routes
	app.use("/api", routes);

	// Global error handler
	app.use((err, req, res, next) => {
		logger.error("Unhandled error:", err);

		// Don't leak error details in production
		const isDevelopment = process.env.NODE_ENV === "development";

		res.status(err.status || 500).json({
			success: false,
			message: err.message || "Internal server error",
			...(isDevelopment && { stack: err.stack }),
			data: null,
		});
	});

	// 404 handler for non-API routes
	app.use("*", (req, res) => {
		res.status(404).json({
			success: false,
			message: "Route not found",
			data: null,
		});
	});

	return app;
};

module.exports = createApp;
