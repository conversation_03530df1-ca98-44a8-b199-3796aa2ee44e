const Offer = require("../models/offer.model");
const logger = require("../utils/logger");

class OfferService {
	/**
	 * Get all offers with optional filtering
	 * @param {Object} filters - Filter options
	 * @param {string|string[]} filters.bank - Bank name(s) to filter by
	 * @param {string|string[]} filters.category - Category(ies) to filter by
	 * @param {string} filters.search - Search term
	 * @param {number} filters.page - Page number for pagination
	 * @param {number} filters.limit - Number of items per page
	 * @returns {Promise<Object>} Paginated offers with metadata
	 */
	async getAllOffers(filters = {}) {
		try {
			const { bank, category, search, page = 1, limit = 20 } = filters;

			// Build query
			let query = { isActive: true };

			// Filter by bank
			if (bank) {
				const bankList = Array.isArray(bank) ? bank : bank.split(",").map((b) => b.trim());
				query.bank = { $in: bankList };
			}

			// Filter by category
			if (category) {
				const categoryList = Array.isArray(category) ? category : category.split(",").map((c) => c.trim());
				query.category = { $in: categoryList };
			}

			// Search functionality
			if (search) {
				// Partial (substring) search on relevant fields
				const regex = new RegExp(search, "i");
				query.$or = [
					{ bank: regex },
					{ merchant: regex },
					{ discount: regex },
					{ description: regex },
					{ category: regex },
				];
			}

			// Calculate pagination
			const skip = (page - 1) * limit;

			// Execute query with pagination
			const [offers, totalCount] = await Promise.all([
				Offer.find(query).sort({ createdAt: -1 }).skip(skip).limit(parseInt(limit)).lean(),
				Offer.countDocuments(query),
			]);

			// Calculate pagination metadata
			const totalPages = Math.ceil(totalCount / limit);
			const hasNextPage = page < totalPages;
			const hasPrevPage = page > 1;

			logger.info(`Retrieved ${offers.length} offers (page ${page}/${totalPages})`);

			return {
				offers,
				pagination: {
					currentPage: parseInt(page),
					totalPages,
					totalCount,
					hasNextPage,
					hasPrevPage,
					limit: parseInt(limit),
				},
			};
		} catch (error) {
			logger.error("Error in getAllOffers:", error);
			throw new Error("Failed to retrieve offers");
		}
	}

	/**
	 * Get offer by ID
	 * @param {string} id - Offer ID
	 * @returns {Promise<Object>} Offer object
	 */
	async getOfferById(id) {
		try {
			const offer = await Offer.findById(id).lean();

			if (!offer) {
				throw new Error("Offer not found");
			}

			if (!offer.isActive) {
				throw new Error("Offer is no longer active");
			}

			logger.info(`Retrieved offer: ${offer.merchant} - ${offer.bank}`);
			return offer;
		} catch (error) {
			logger.error(`Error in getOfferById for ID ${id}:`, error);
			throw error;
		}
	}

	/**
	 * Create new offer
	 * @param {Object} offerData - Offer data
	 * @returns {Promise<Object>} Created offer
	 */
	async createOffer(offerData) {
		try {
			const offer = new Offer(offerData);
			const savedOffer = await offer.save();

			logger.info(`Created new offer: ${savedOffer.merchant} - ${savedOffer.bank}`);
			return savedOffer.toObject();
		} catch (error) {
			logger.error("Error in createOffer:", error);
			throw new Error("Failed to create offer");
		}
	}

	/**
	 * Update offer by ID
	 * @param {string} id - Offer ID
	 * @param {Object} updateData - Data to update
	 * @returns {Promise<Object>} Updated offer
	 */
	async updateOffer(id, updateData) {
		try {
			const offer = await Offer.findByIdAndUpdate(id, updateData, { new: true, runValidators: true }).lean();

			if (!offer) {
				throw new Error("Offer not found");
			}

			logger.info(`Updated offer: ${offer.merchant} - ${offer.bank}`);
			return offer;
		} catch (error) {
			logger.error(`Error in updateOffer for ID ${id}:`, error);
			throw error;
		}
	}

	/**
	 * Delete offer by ID (soft delete)
	 * @param {string} id - Offer ID
	 * @returns {Promise<Object>} Deleted offer
	 */
	async deleteOffer(id) {
		try {
			const offer = await Offer.findByIdAndUpdate(id, { isActive: false }, { new: true }).lean();

			if (!offer) {
				throw new Error("Offer not found");
			}

			logger.info(`Soft deleted offer: ${offer.merchant} - ${offer.bank}`);
			return offer;
		} catch (error) {
			logger.error(`Error in deleteOffer for ID ${id}:`, error);
			throw error;
		}
	}

	/**
	 * Get all unique banks
	 * @returns {Promise<string[]>} Array of bank names
	 */
	async getAllBanks() {
		try {
			const banks = await Offer.distinct("bank", { isActive: true });
			logger.info(`Retrieved ${banks.length} unique banks`);
			return banks.sort();
		} catch (error) {
			logger.error("Error in getAllBanks:", error);
			throw new Error("Failed to retrieve banks");
		}
	}

	/**
	 * Get all unique categories
	 * @returns {Promise<string[]>} Array of category names
	 */
	async getAllCategories() {
		try {
			const categories = await Offer.distinct("category", { isActive: true });
			logger.info(`Retrieved ${categories.length} unique categories`);
			return categories.sort();
		} catch (error) {
			logger.error("Error in getAllCategories:", error);
			throw new Error("Failed to retrieve categories");
		}
	}
}

module.exports = new OfferService();
