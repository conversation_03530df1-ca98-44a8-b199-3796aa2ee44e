const mongoose = require("mongoose");

const offerSchema = new mongoose.Schema(
    {
        bank: {
            type: String,
            required: [true, "Bank name is required"],
            trim: true,
            enum: [
                "BOC",
                "DFCC Bank", 
                "People's Bank",
                "Commercial Bank",
                "Sampath Bank",
                "NTB",
                "NDB",
                "HSBC",
                "Seylan Bank",
                "HNB",
                "Union Bank",
                "Cargills Bank",
                "Pan Asia Bank",
                "LOLC"
            ],
        },
        merchant: {
            type: String,
            required: [true, "Merchant name is required"],
            trim: true,
        },
        discount: {
            type: String,
            required: [true, "Discount information is required"],
            trim: true,
        },
        description: {
            type: String,
            required: [true, "Description is required"],
            trim: true,
        },
        validUntil: {
            type: String,
            required: [true, "Valid until date is required"],
            trim: true,
        },
        category: {
            type: String,
            required: [true, "Category is required"],
            trim: true,
            enum: [
                "Travel & Leisure",
                "Shopping",
                "Dining",
                "Health & Beauty",
                "Supermarkets",
                "Entertainment",
                "Fuel",
                "Online Shopping",
                "Education",
                "Utilities"
            ],
        },
        imageUrl: {
            type: String,
            trim: true,
            default: "",
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        terms: {
            type: String,
            trim: true,
            default: "",
        },
        minimumSpend: {
            type: Number,
            min: 0,
            default: 0,
        },
        maximumDiscount: {
            type: Number,
            min: 0,
        },
    },
    {
        timestamps: true,
        toJSON: { virtuals: true },
        toObject: { virtuals: true },
    }
);

// Indexes for better query performance
offerSchema.index({ bank: 1 });
offerSchema.index({ category: 1 });
offerSchema.index({ isActive: 1 });
offerSchema.index({ validUntil: 1 });

// Text index for search functionality
offerSchema.index({
    bank: "text",
    merchant: "text",
    description: "text",
    category: "text",
    discount: "text",
});

// Virtual for formatted discount
offerSchema.virtual("formattedDiscount").get(function () {
    return this.discount;
});

// Instance method to check if offer is still valid
offerSchema.methods.isValidOffer = function () {
    const currentDate = new Date();
    const validDate = new Date(this.validUntil);
    return this.isActive && validDate >= currentDate;
};

// Static method to find offers by bank
offerSchema.statics.findByBank = function (bankNames) {
    const bankList = Array.isArray(bankNames) ? bankNames : [bankNames];
    return this.find({
        bank: { $in: bankList },
        isActive: true,
    });
};

// Static method to find offers by category
offerSchema.statics.findByCategory = function (categories) {
    const categoryList = Array.isArray(categories) ? categories : [categories];
    return this.find({
        category: { $in: categoryList },
        isActive: true,
    });
};

// Pre-save middleware
offerSchema.pre("save", function (next) {
    // Capitalize first letter of bank and merchant names
    if (this.bank) {
        this.bank = this.bank.trim();
    }
    if (this.merchant) {
        this.merchant = this.merchant.trim();
    }
    next();
});

const Offer = mongoose.model("Offer", offerSchema);

module.exports = Offer;
