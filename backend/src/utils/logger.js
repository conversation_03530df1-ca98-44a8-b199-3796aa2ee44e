const logLevel = process.env.LOG_LEVEL || "info";

const levels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
};

const colors = {
    error: "\x1b[31m", // Red
    warn: "\x1b[33m",  // Yellow
    info: "\x1b[36m",  // <PERSON>an
    debug: "\x1b[35m", // Magenta
    reset: "\x1b[0m",  // Reset
};

const shouldLog = (level) => {
    return levels[level] <= levels[logLevel];
};

const formatMessage = (level, message, ...args) => {
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.reset;
    const formattedMessage = typeof message === "object" ? JSON.stringify(message, null, 2) : message;
    
    return `${color}[${timestamp}] ${level.toUpperCase()}: ${formattedMessage}${colors.reset}`;
};

const logger = {
    error: (message, ...args) => {
        if (shouldLog("error")) {
            console.error(formatMessage("error", message, ...args));
            if (args.length > 0) {
                console.error(...args);
            }
        }
    },
    
    warn: (message, ...args) => {
        if (shouldLog("warn")) {
            console.warn(formatMessage("warn", message, ...args));
            if (args.length > 0) {
                console.warn(...args);
            }
        }
    },
    
    info: (message, ...args) => {
        if (shouldLog("info")) {
            console.log(formatMessage("info", message, ...args));
            if (args.length > 0) {
                console.log(...args);
            }
        }
    },
    
    debug: (message, ...args) => {
        if (shouldLog("debug")) {
            console.log(formatMessage("debug", message, ...args));
            if (args.length > 0) {
                console.log(...args);
            }
        }
    },
};

module.exports = logger;
