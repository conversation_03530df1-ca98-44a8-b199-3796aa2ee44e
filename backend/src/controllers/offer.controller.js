const offerService = require("../services/offer.service");
const logger = require("../utils/logger");

/**
 * Get all credit card offers with filtering and pagination
 */
const getAllOffers = async (req, res) => {
    try {
        const { bank, category, search, page, limit } = req.query;
        
        const result = await offerService.getAllOffers({
            bank,
            category,
            search,
            page: parseInt(page) || 1,
            limit: parseInt(limit) || 20
        });
        
        res.status(200).json({
            success: true,
            data: result.offers,
            pagination: result.pagination,
            message: "Offers retrieved successfully"
        });
    } catch (error) {
        logger.error("Error in getAllOffers controller:", error);
        res.status(500).json({
            success: false,
            message: error.message || "Internal server error",
            data: null
        });
    }
};

/**
 * Get a specific credit card offer by ID
 */
const getOfferById = async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id) {
            return res.status(400).json({
                success: false,
                message: "Offer ID is required",
                data: null
            });
        }
        
        const offer = await offerService.getOfferById(id);
        
        res.status(200).json({
            success: true,
            data: offer,
            message: "Offer retrieved successfully"
        });
    } catch (error) {
        logger.error("Error in getOfferById controller:", error);
        
        if (error.message === "Offer not found" || error.message === "Offer is no longer active") {
            return res.status(404).json({
                success: false,
                message: error.message,
                data: null
            });
        }
        
        res.status(500).json({
            success: false,
            message: "Internal server error",
            data: null
        });
    }
};

/**
 * Create a new credit card offer
 */
const createOffer = async (req, res) => {
    try {
        const offerData = req.body;
        
        const newOffer = await offerService.createOffer(offerData);
        
        res.status(201).json({
            success: true,
            data: newOffer,
            message: "Offer created successfully"
        });
    } catch (error) {
        logger.error("Error in createOffer controller:", error);
        
        if (error.name === "ValidationError") {
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: Object.values(error.errors).map(err => err.message),
                data: null
            });
        }
        
        res.status(500).json({
            success: false,
            message: error.message || "Internal server error",
            data: null
        });
    }
};

/**
 * Update an existing credit card offer
 */
const updateOffer = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        
        if (!id) {
            return res.status(400).json({
                success: false,
                message: "Offer ID is required",
                data: null
            });
        }
        
        const updatedOffer = await offerService.updateOffer(id, updateData);
        
        res.status(200).json({
            success: true,
            data: updatedOffer,
            message: "Offer updated successfully"
        });
    } catch (error) {
        logger.error("Error in updateOffer controller:", error);
        
        if (error.message === "Offer not found") {
            return res.status(404).json({
                success: false,
                message: error.message,
                data: null
            });
        }
        
        if (error.name === "ValidationError") {
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: Object.values(error.errors).map(err => err.message),
                data: null
            });
        }
        
        res.status(500).json({
            success: false,
            message: "Internal server error",
            data: null
        });
    }
};

/**
 * Delete a credit card offer (soft delete)
 */
const deleteOffer = async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id) {
            return res.status(400).json({
                success: false,
                message: "Offer ID is required",
                data: null
            });
        }
        
        const deletedOffer = await offerService.deleteOffer(id);
        
        res.status(200).json({
            success: true,
            data: deletedOffer,
            message: "Offer deleted successfully"
        });
    } catch (error) {
        logger.error("Error in deleteOffer controller:", error);
        
        if (error.message === "Offer not found") {
            return res.status(404).json({
                success: false,
                message: error.message,
                data: null
            });
        }
        
        res.status(500).json({
            success: false,
            message: "Internal server error",
            data: null
        });
    }
};

/**
 * Get all available banks
 */
const getAllBanks = async (req, res) => {
    try {
        const banks = await offerService.getAllBanks();
        
        res.status(200).json({
            success: true,
            data: banks,
            message: "Banks retrieved successfully"
        });
    } catch (error) {
        logger.error("Error in getAllBanks controller:", error);
        res.status(500).json({
            success: false,
            message: error.message || "Internal server error",
            data: null
        });
    }
};

/**
 * Get all available categories
 */
const getAllCategories = async (req, res) => {
    try {
        const categories = await offerService.getAllCategories();
        
        res.status(200).json({
            success: true,
            data: categories,
            message: "Categories retrieved successfully"
        });
    } catch (error) {
        logger.error("Error in getAllCategories controller:", error);
        res.status(500).json({
            success: false,
            message: error.message || "Internal server error",
            data: null
        });
    }
};

module.exports = {
    getAllOffers,
    getOfferById,
    createOffer,
    updateOffer,
    deleteOffer,
    getAllBanks,
    getAllCategories
};
