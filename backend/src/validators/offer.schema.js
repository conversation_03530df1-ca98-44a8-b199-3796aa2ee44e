// Validation schemas for offer-related requests
// This will be implemented with <PERSON><PERSON><PERSON> or <PERSON><PERSON> for request validation

/**
 * Schema for creating a new offer
 * TODO: Implement with <PERSON>O<PERSON> or <PERSON>od
 */
const createOfferSchema = {
    // bank: required string, must be one of the allowed banks
    // merchant: required string, min 2 chars
    // discount: required string
    // description: required string, min 10 chars
    // validUntil: required string, valid date format
    // category: required string, must be one of the allowed categories
    // imageUrl: optional string, valid URL format
    // terms: optional string
    // minimumSpend: optional number, min 0
    // maximumDiscount: optional number, min 0
};

/**
 * Schema for updating an offer
 * TODO: Implement with JO<PERSON> or Zod
 */
const updateOfferSchema = {
    // All fields optional but with same validation rules as create
    // At least one field must be provided
};

/**
 * Schema for query parameters
 * TODO: Implement with JO<PERSON> or Zod
 */
const querySchema = {
    // bank: optional string or array of strings
    // category: optional string or array of strings
    // search: optional string, min 2 chars
    // page: optional number, min 1
    // limit: optional number, min 1, max 100
};

module.exports = {
    createOfferSchema,
    updateOfferSchema,
    querySchema
};
