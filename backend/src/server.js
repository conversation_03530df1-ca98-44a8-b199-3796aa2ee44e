require("dotenv").config();

const createApp = require("./app");
const connectDB = require("./config/db");
const logger = require("./utils/logger");

const startServer = async () => {
	try {
		// Connect to MongoDB
		await connectDB();

		// Create Express app
		const app = createApp();

		// Get port from environment or default to 3000
		const PORT = process.env.PORT || 3000;

		// Start server
		const server = app.listen(PORT, () => {
			logger.info(`🚀 Server running on port ${PORT}`);
			logger.info(`📱 Environment: ${process.env.NODE_ENV || "development"}`);
			logger.info(`🌐 API Base URL: http://localhost:${PORT}/api`);
		});

		// Graceful shutdown handling
		const gracefulShutdown = (signal) => {
			logger.info(`Received ${signal}. Starting graceful shutdown...`);

			server.close((err) => {
				if (err) {
					logger.error("Error during server shutdown:", err);
					process.exit(1);
				}

				logger.info("Server closed successfully");
				process.exit(0);
			});
		};

		// Handle shutdown signals
		process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
		process.on("SIGINT", () => gracefulShutdown("SIGINT"));

		// Handle uncaught exceptions
		process.on("uncaughtException", (err) => {
			logger.error("Uncaught Exception:", err);
			process.exit(1);
		});

		// Handle unhandled promise rejections
		process.on("unhandledRejection", (reason, promise) => {
			logger.error("Unhandled Rejection at:", promise, "reason:", reason);
			process.exit(1);
		});
	} catch (error) {
		logger.error("Failed to start server:", error);
		process.exit(1);
	}
};

// Start the server
startServer();
