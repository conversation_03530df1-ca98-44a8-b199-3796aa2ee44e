# Bank Offers API

A RESTful API for managing credit card offers from Sri Lankan banks. Built with Node.js, Express, and MongoDB.

## Features

- 🏦 Manage credit card offers from 14 major Sri Lankan banks
- 🔍 Advanced filtering by bank, category, and search terms
- 📄 Pagination support for large datasets
- 🏷️ Category-based organization of offers
- 🔒 Prepared for authentication and authorization
- 📊 Comprehensive logging and error handling
- 🧪 Test-ready structure
- 🚀 Production-ready architecture

## Supported Banks

- BOC (Bank of Ceylon)
- DFCC Bank
- People's Bank
- Commercial Bank
- Sampath Bank
- NTB (Nations Trust Bank)
- NDB (National Development Bank)
- HSBC
- Seylan Bank
- HNB (Hatton National Bank)
- Union Bank
- Cargills Bank
- Pan Asia Bank
- LOLC

## Project Structure

```
backend/
├── package.json
├── .env                 # secrets & per-env overrides
├── .env.example         # template for new devs
├── .gitignore
├── README.md
├── src/
│   ├── server.js        # entry point: loads env, connects DB, starts HTTP server
│   ├── app.js           # creates & configures the Express app
│   │
│   ├── config/          # static configuration helpers
│   │   └── db.js        # Mongoose connection logic
│   │
│   ├── models/          # Mongoose schemas & model hooks
│   │   └── offer.model.js
│   │
│   ├── controllers/     # thin "route handlers"; 1-file-per-resource
│   │   └── offer.controller.js
│   │
│   ├── services/        # business logic, orchestrates models & utilities
│   │   └── offer.service.js
│   │
│   ├── routes/          # Express routers, grouped by resource or version
│   │   ├── v1/
│   │   │   └── offer.routes.js
│   │   └── index.js     # mounts versioned routers on the app
│   │
│   ├── middlewares/     # custom Express middleware (auth, validation, etc.)
│   │   ├── auth.middleware.js
│   │   └── validate.middleware.js
│   │
│   ├── utils/           # helpers (logger, response builder, hash, etc.)
│   │   └── logger.js
│   │
│   ├── validators/      # JOI/Zod schemas to validate request bodies & params
│   │   └── offer.schema.js
│   │
│   └── tests/           # Jest/Mocha tests mirror src/ structure
│       └── offer.test.js
│
└── scripts/             # one-off or CI scripts (seed DB, create admin, etc.)
    └── seed.js
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Update `.env` with your configuration:
   ```env
   PORT=3000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/bank-offers
   CORS_ORIGIN=http://localhost:3001
   LOG_LEVEL=info
   ```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Seed Database
```bash
npm run seed
```

## API Endpoints

### Base URL
```
http://localhost:3000/api
```

### Offers

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/v1/offers` | Get all offers with filtering and pagination |
| GET | `/v1/offers/:id` | Get specific offer by ID |
| POST | `/v1/offers` | Create new offer |
| PUT | `/v1/offers/:id` | Update offer by ID |
| DELETE | `/v1/offers/:id` | Delete offer by ID (soft delete) |
| GET | `/v1/offers/banks` | Get all available banks |
| GET | `/v1/offers/categories` | Get all available categories |

### Query Parameters for GET `/v1/offers`

- `bank` - Filter by bank name(s) (comma-separated)
- `category` - Filter by category(ies) (comma-separated)
- `search` - Search term for full-text search
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

### Example Requests

```bash
# Get all offers
curl http://localhost:3000/api/v1/offers

# Filter by bank
curl "http://localhost:3000/api/v1/offers?bank=BOC,HSBC"

# Filter by category
curl "http://localhost:3000/api/v1/offers?category=Dining"

# Search offers
curl "http://localhost:3000/api/v1/offers?search=hotel"

# Pagination
curl "http://localhost:3000/api/v1/offers?page=2&limit=10"

# Combined filters
curl "http://localhost:3000/api/v1/offers?bank=Commercial&category=Travel&page=1&limit=5"
```

## Response Format

All API responses follow this structure:

```json
{
  "success": true,
  "data": [...],
  "message": "Success message",
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalCount": 50,
    "hasNextPage": true,
    "hasPrevPage": false,
    "limit": 10
  }
}
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment | development |
| `MONGODB_URI` | MongoDB connection string | mongodb://localhost:27017/bank-offers |
| `CORS_ORIGIN` | CORS origin | http://localhost:3001 |
| `LOG_LEVEL` | Logging level | info |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

ISC
