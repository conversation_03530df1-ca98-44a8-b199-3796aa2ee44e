{"name": "bank-offers-api", "version": "1.0.0", "description": "Bank Offers API - A RESTful API for managing credit card offers from Sri Lankan banks", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "seed": "node scripts/seed.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "build": "echo \"Build process not configured yet\""}, "keywords": ["bank", "offers", "credit-card", "sri-lanka", "api", "express", "mongodb", "mongoose"], "author": "Bank Offers Team", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "mongoose": "^8.15.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}