require("dotenv").config();
const mongoose = require("mongoose");
const Offer = require("../src/models/offer.model");
const logger = require("../src/utils/logger");

// Sample credit card offers data
const sampleOffers = [
    {
        bank: "Commercial Bank",
        merchant: "Hilton Colombo",
        discount: "20% OFF",
        description: "20% off on total bill for Commercial Bank credit card holders",
        validUntil: "31 Dec 2025",
        category: "Travel & Leisure",
        imageUrl: "https://example.com/hilton-colombo.jpg",
        minimumSpend: 5000,
        maximumDiscount: 10000,
    },
    {
        bank: "Sampath Bank",
        merchant: "Cinnamon Grand Hotel",
        discount: "15% OFF",
        description: "15% off on room rates and dining for Sampath Bank credit card holders",
        validUntil: "30 Nov 2025",
        category: "Travel & Leisure",
        imageUrl: "https://example.com/cinnamon-grand.jpg",
        minimumSpend: 10000,
    },
    {
        bank: "HNB",
        merchant: "Odel",
        discount: "10% OFF",
        description: "10% off on all purchases above Rs. 5,000 for HNB credit card holders",
        validUntil: "31 Oct 2025",
        category: "Shopping",
        imageUrl: "https://example.com/odel.jpg",
        minimumSpend: 5000,
        maximumDiscount: 5000,
    },
    {
        bank: "BOC",
        merchant: "Joe's Resort Unawatuna",
        discount: "20% OFF",
        description: "20% off on full board, half board and bed & breakfast basis for BOC Credit & Debit Cardholders",
        validUntil: "31 Dec 2025",
        category: "Travel & Leisure",
        imageUrl: "https://example.com/joes-resort.jpg",
        minimumSpend: 15000,
    },
    {
        bank: "DFCC Bank",
        merchant: "The Gallery Cafe",
        discount: "15% OFF",
        description: "15% off on total bill for DFCC Bank credit card holders",
        validUntil: "30 Sep 2025",
        category: "Dining",
        imageUrl: "https://example.com/gallery-cafe.jpg",
        minimumSpend: 2000,
        maximumDiscount: 3000,
    },
    {
        bank: "People's Bank",
        merchant: "Spa Ceylon",
        discount: "20% OFF",
        description: "20% off on all spa treatments and products for People's Bank credit card holders",
        validUntil: "31 Aug 2025",
        category: "Health & Beauty",
        imageUrl: "https://example.com/spa-ceylon.jpg",
        minimumSpend: 3000,
        maximumDiscount: 8000,
    },
    {
        bank: "NTB",
        merchant: "Keells Supermarket",
        discount: "15% OFF",
        description: "15% off on all purchases above Rs. 7,500 on weekends for NTB credit card holders",
        validUntil: "31 Jul 2025",
        category: "Supermarkets",
        imageUrl: "https://example.com/keells.jpg",
        minimumSpend: 7500,
        maximumDiscount: 2500,
    },
    {
        bank: "NDB",
        merchant: "Centauria Wild Udawalawa",
        discount: "20% OFF",
        description: "20% off for NDB Credit & 15% off for Debit Cardholders",
        validUntil: "31 Aug 2025",
        category: "Travel & Leisure",
        imageUrl: "https://example.com/centauria.jpg",
        minimumSpend: 12000,
    },
    {
        bank: "HSBC",
        merchant: "Burger King",
        discount: "25% OFF",
        description: "25% off on total bill for HSBC credit card holders every Wednesday",
        validUntil: "30 Sep 2025",
        category: "Dining",
        imageUrl: "https://example.com/burger-king.jpg",
        minimumSpend: 1000,
        maximumDiscount: 1500,
    },
    {
        bank: "Seylan Bank",
        merchant: "Citrus Waskaduwa",
        discount: "15% OFF",
        description: "15% off on BB, HB & FB basis for Seylan Bank credit card holders",
        validUntil: "31 Jul 2025",
        category: "Travel & Leisure",
        imageUrl: "https://example.com/citrus-waskaduwa.jpg",
        minimumSpend: 8000,
    },
    {
        bank: "Union Bank",
        merchant: "Subway",
        discount: "15% OFF",
        description: "15% off on total bill for Union Bank credit card holders",
        validUntil: "31 May 2025",
        category: "Dining",
        imageUrl: "https://example.com/subway.jpg",
        minimumSpend: 800,
        maximumDiscount: 1200,
    },
    {
        bank: "Cargills Bank",
        merchant: "Cargills Food City",
        discount: "25% OFF",
        description: "25% off for Cargills Bank Credit Cards on Fresh Vegetables, Fruits & Seafood on Wednesdays & Sundays",
        validUntil: "28 May 2025",
        category: "Supermarkets",
        imageUrl: "https://example.com/cargills-food-city.jpg",
        minimumSpend: 5000,
        maximumDiscount: 3000,
    },
    {
        bank: "Pan Asia Bank",
        merchant: "Nolimit",
        discount: "10% OFF",
        description: "10% off on all purchases above Rs. 5,000 for Pan Asia Bank credit card holders",
        validUntil: "30 Jun 2025",
        category: "Shopping",
        imageUrl: "https://example.com/nolimit.jpg",
        minimumSpend: 5000,
        maximumDiscount: 4000,
    },
    {
        bank: "LOLC",
        merchant: "Maya Clinic",
        discount: "20% OFF",
        description: "20% off on aesthetic, dental & plastic surgery services for LOLC credit card holders",
        validUntil: "31 Dec 2025",
        category: "Health & Beauty",
        imageUrl: "https://example.com/maya-clinic.jpg",
        minimumSpend: 10000,
        maximumDiscount: 25000,
    },
];

const seedDatabase = async () => {
    try {
        // Connect to MongoDB
        const mongoURI = process.env.MONGODB_URI || "mongodb://localhost:27017/bank-offers";
        await mongoose.connect(mongoURI);
        logger.info("Connected to MongoDB for seeding");

        // Clear existing offers
        await Offer.deleteMany({});
        logger.info("Cleared existing offers");

        // Insert sample offers
        const insertedOffers = await Offer.insertMany(sampleOffers);
        logger.info(`Successfully seeded ${insertedOffers.length} offers`);

        // Close connection
        await mongoose.connection.close();
        logger.info("Database seeding completed successfully");
        
    } catch (error) {
        logger.error("Error seeding database:", error);
        process.exit(1);
    }
};

// Run the seeding script
if (require.main === module) {
    seedDatabase();
}

module.exports = { seedDatabase, sampleOffers };
