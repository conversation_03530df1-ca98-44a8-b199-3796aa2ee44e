require("dotenv").config();
const mongoose = require("mongoose");
const Offer = require("../src/models/offer.model");
const logger = require("../src/utils/logger");

// Sample credit card offers data
const sampleOffers = [
	{
		bank: "Commercial Bank",
		merchant: "Hilton Colombo",
		discount: "20% OFF",
		description: "20% off on total bill for Commercial Bank credit card holders",
		validUntil: "31 Dec 2025",
		category: "Travel & Leisure",
		imageUrl: "https://images.unsplash.com/photo-*************-6a8506099945?w=400&h=300&fit=crop",
		minimumSpend: 5000,
		maximumDiscount: 10000,
	},
	{
		bank: "Sampath Bank",
		merchant: "Cinnamon Grand Hotel",
		discount: "15% OFF",
		description: "15% off on room rates and dining for Sampath Bank credit card holders",
		validUntil: "30 Nov 2025",
		category: "Travel & Leisure",
		imageUrl: "https://images.unsplash.com/photo-**********-ff40c63fe5fa?w=400&h=300&fit=crop",
		minimumSpend: 10000,
	},
	{
		bank: "HNB",
		merchant: "Odel",
		discount: "10% OFF",
		description: "10% off on all purchases above Rs. 5,000 for HNB credit card holders",
		validUntil: "31 Oct 2025",
		category: "Shopping",
		imageUrl: "https://images.unsplash.com/photo-*************-64674bd600d8?w=400&h=300&fit=crop",
		minimumSpend: 5000,
		maximumDiscount: 5000,
	},
	{
		bank: "BOC",
		merchant: "Joe's Resort Unawatuna",
		discount: "20% OFF",
		description: "20% off on full board, half board and bed & breakfast basis for BOC Credit & Debit Cardholders",
		validUntil: "31 Dec 2025",
		category: "Travel & Leisure",
		imageUrl: "https://images.unsplash.com/photo-*************-112f2f40a3f4?w=400&h=300&fit=crop",
		minimumSpend: 15000,
	},
	{
		bank: "DFCC Bank",
		merchant: "The Gallery Cafe",
		discount: "15% OFF",
		description: "15% off on total bill for DFCC Bank credit card holders",
		validUntil: "30 Sep 2025",
		category: "Dining",
		imageUrl: "https://images.unsplash.com/photo-*************-4c7edcad34c4?w=400&h=300&fit=crop",
		minimumSpend: 2000,
		maximumDiscount: 3000,
	},
	{
		bank: "People's Bank",
		merchant: "Spa Ceylon",
		discount: "20% OFF",
		description: "20% off on all spa treatments and products for People's Bank credit card holders",
		validUntil: "31 Aug 2025",
		category: "Health & Beauty",
		imageUrl: "https://images.unsplash.com/photo-**********-4ab6ce6db874?w=400&h=300&fit=crop",
		minimumSpend: 3000,
		maximumDiscount: 8000,
	},
	{
		bank: "NTB",
		merchant: "Keells Supermarket",
		discount: "15% OFF",
		description: "15% off on all purchases above Rs. 7,500 on weekends for NTB credit card holders",
		validUntil: "31 Jul 2025",
		category: "Supermarkets",
		imageUrl: "https://images.unsplash.com/photo-*************-48f60103fc96?w=400&h=300&fit=crop",
		minimumSpend: 7500,
		maximumDiscount: 2500,
	},
	{
		bank: "NDB",
		merchant: "Centauria Wild Udawalawa",
		discount: "20% OFF",
		description: "20% off for NDB Credit & 15% off for Debit Cardholders",
		validUntil: "31 Aug 2025",
		category: "Travel & Leisure",
		imageUrl: "https://images.unsplash.com/photo-**********-9f761d040a94?w=400&h=300&fit=crop",
		minimumSpend: 12000,
	},
	{
		bank: "HSBC",
		merchant: "Burger King",
		discount: "25% OFF",
		description: "25% off on total bill for HSBC credit card holders every Wednesday",
		validUntil: "30 Sep 2025",
		category: "Dining",
		imageUrl: "https://images.unsplash.com/photo-*************-18b5b1457add?w=400&h=300&fit=crop",
		minimumSpend: 1000,
		maximumDiscount: 1500,
	},
	{
		bank: "Seylan Bank",
		merchant: "Citrus Waskaduwa",
		discount: "15% OFF",
		description: "15% off on BB, HB & FB basis for Seylan Bank credit card holders",
		validUntil: "31 Jul 2025",
		category: "Travel & Leisure",
		imageUrl: "https://images.unsplash.com/photo-*************-33c89424de2d?w=400&h=300&fit=crop",
		minimumSpend: 8000,
	},
	{
		bank: "Union Bank",
		merchant: "Subway",
		discount: "15% OFF",
		description: "15% off on total bill for Union Bank credit card holders",
		validUntil: "31 May 2025",
		category: "Dining",
		imageUrl: "https://images.unsplash.com/photo-**********-367ea4eb4db5?w=400&h=300&fit=crop",
		minimumSpend: 800,
		maximumDiscount: 1200,
	},
	{
		bank: "Cargills Bank",
		merchant: "Cargills Food City",
		discount: "25% OFF",
		description:
			"25% off for Cargills Bank Credit Cards on Fresh Vegetables, Fruits & Seafood on Wednesdays & Sundays",
		validUntil: "28 May 2025",
		category: "Supermarkets",
		imageUrl: "https://images.unsplash.com/photo-**********-92c53300491e?w=400&h=300&fit=crop",
		minimumSpend: 5000,
		maximumDiscount: 3000,
	},
	{
		bank: "Pan Asia Bank",
		merchant: "Nolimit",
		discount: "10% OFF",
		description: "10% off on all purchases above Rs. 5,000 for Pan Asia Bank credit card holders",
		validUntil: "30 Jun 2025",
		category: "Shopping",
		imageUrl: "https://images.unsplash.com/photo-*************-062f824d29cc?w=400&h=300&fit=crop",
		minimumSpend: 5000,
		maximumDiscount: 4000,
	},
	{
		bank: "LOLC",
		merchant: "Maya Clinic",
		discount: "20% OFF",
		description: "20% off on aesthetic, dental & plastic surgery services for LOLC credit card holders",
		validUntil: "31 Dec 2025",
		category: "Health & Beauty",
		imageUrl: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop",
		minimumSpend: 10000,
		maximumDiscount: 25000,
	},
	// Add some offers without images to test fallback behavior
	{
		bank: "Commercial Bank",
		merchant: "Pizza Hut",
		discount: "30% OFF",
		description: "30% off on all pizzas for Commercial Bank credit card holders on weekends",
		validUntil: "31 Dec 2025",
		category: "Dining",
		// No imageUrl to test fallback
		minimumSpend: 1500,
		maximumDiscount: 2000,
	},
	{
		bank: "Sampath Bank",
		merchant: "Softlogic Showroom",
		discount: "12% OFF",
		description: "12% off on all electronics and home appliances for Sampath Bank credit card holders",
		validUntil: "30 Nov 2025",
		category: "Shopping",
		// No imageUrl to test fallback
		minimumSpend: 25000,
		maximumDiscount: 15000,
	},
];

const seedDatabase = async () => {
	try {
		// Connect to MongoDB
		const mongoURI = process.env.MONGODB_URI || "mongodb://localhost:27017/bank-offers";
		await mongoose.connect(mongoURI);
		logger.info("Connected to MongoDB for seeding");

		// Clear existing offers
		await Offer.deleteMany({});
		logger.info("Cleared existing offers");

		// Insert sample offers
		const insertedOffers = await Offer.insertMany(sampleOffers);
		logger.info(`Successfully seeded ${insertedOffers.length} offers`);

		// Close connection
		await mongoose.connection.close();
		logger.info("Database seeding completed successfully");
	} catch (error) {
		logger.error("Error seeding database:", error);
		process.exit(1);
	}
};

// Run the seeding script
if (require.main === module) {
	seedDatabase();
}

module.exports = { seedDatabase, sampleOffers };
