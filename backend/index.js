const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.get("/", (req, res) => {
	res.send("Bank Offers API is running");
});

// Sample credit card offers data
const creditCardOffers = [
	{
		id: 1,
		bank: "Commercial Bank",
		merchant: "Hilton Colombo",
		discount: "20% OFF",
		description: "20% off on total bill for Commercial Bank credit card holders",
		validUntil: "31 Dec 2025",
		category: "Travel & Leisure",
		imageUrl: "https://example.com/hilton-colombo.jpg",
	},
	{
		id: 2,
		bank: "Sampath Bank",
		merchant: "Cinnamon Grand Hotel",
		discount: "15% OFF",
		description: "15% off on room rates and dining for Sampath Bank credit card holders",
		validUntil: "30 Nov 2025",
		category: "Travel & Leisure",
		imageUrl: "https://example.com/cinnamon-grand.jpg",
	},
	{
		id: 3,
		bank: "HNB",
		merchant: "Odel",
		discount: "10% OFF",
		description: "10% off on all purchases above Rs. 5,000 for HNB credit card holders",
		validUntil: "31 Oct 2025",
		category: "Shopping",
		imageUrl: "https://example.com/odel.jpg",
	},
	{
		id: 4,
		bank: "BOC",
		merchant: "Joe's Resort Unawatuna",
		discount: "20% OFF",
		description: "20% off on full board, half board and bed & breakfast basis for BOC Credit & Debit Cardholders",
		validUntil: "31 Dec 2025",
		category: "Travel & Leisure",
		imageUrl: "https://example.com/joes-resort.jpg",
	},
	{
		id: 5,
		bank: "DFCC Bank",
		merchant: "The Gallery Cafe",
		discount: "15% OFF",
		description: "15% off on total bill for DFCC Bank credit card holders",
		validUntil: "30 Sep 2025",
		category: "Dining",
		imageUrl: "https://example.com/gallery-cafe.jpg",
	},
	{
		id: 6,
		bank: "People's Bank",
		merchant: "Spa Ceylon",
		discount: "20% OFF",
		description: "20% off on all spa treatments and products for People's Bank credit card holders",
		validUntil: "31 Aug 2025",
		category: "Health & Beauty",
		imageUrl: "https://example.com/spa-ceylon.jpg",
	},
	{
		id: 7,
		bank: "NTB",
		merchant: "Keells Supermarket",
		discount: "15% OFF",
		description: "15% off on all purchases above Rs. 7,500 on weekends for NTB credit card holders",
		validUntil: "31 Jul 2025",
		category: "Supermarkets",
		imageUrl: "https://example.com/keells.jpg",
	},
	{
		id: 8,
		bank: "NDB",
		merchant: "Centauria Wild Udawalawa",
		discount: "20% OFF",
		description: "20% off for NDB Credit & 15% off for Debit Cardholders",
		validUntil: "31 Aug 2025",
		category: "Travel & Leisure",
		imageUrl: "https://example.com/centauria.jpg",
	},
	{
		id: 9,
		bank: "HSBC",
		merchant: "Burger King",
		discount: "25% OFF",
		description: "25% off on total bill for HSBC credit card holders every Wednesday",
		validUntil: "30 Sep 2025",
		category: "Dining",
		imageUrl: "https://example.com/burger-king.jpg",
	},
	{
		id: 10,
		bank: "Seylan Bank",
		merchant: "Citrus Waskaduwa",
		discount: "15% OFF",
		description: "15% off on BB, HB & FB basis for Seylan Bank credit card holders",
		validUntil: "31 Jul 2025",
		category: "Travel & Leisure",
		imageUrl: "https://example.com/citrus-waskaduwa.jpg",
	},
	{
		id: 11,
		bank: "Union Bank",
		merchant: "Subway",
		discount: "15% OFF",
		description: "15% off on total bill for Union Bank credit card holders",
		validUntil: "31 May 2025",
		category: "Dining",
		imageUrl: "https://example.com/subway.jpg",
	},
	{
		id: 12,
		bank: "Cargills Bank",
		merchant: "Cargills Food City",
		discount: "25% OFF",
		description:
			"25% off for Cargills Bank Credit Cards on Fresh Vegetables, Fruits & Seafood on Wednesdays & Sundays",
		validUntil: "28 May 2025",
		category: "Supermarkets",
		imageUrl: "https://example.com/cargills-food-city.jpg",
	},
	{
		id: 13,
		bank: "Pan Asia Bank",
		merchant: "Nolimit",
		discount: "10% OFF",
		description: "10% off on all purchases above Rs. 5,000 for Pan Asia Bank credit card holders",
		validUntil: "30 Jun 2025",
		category: "Shopping",
		imageUrl: "https://example.com/nolimit.jpg",
	},
	{
		id: 14,
		bank: "LOLC",
		merchant: "Maya Clinic",
		discount: "20% OFF",
		description: "20% off on aesthetic, dental & plastic surgery services for LOLC credit card holders",
		validUntil: "31 Dec 2025",
		category: "Health & Beauty",
		imageUrl: "https://example.com/maya-clinic.jpg",
	},
];

// API endpoint to get all credit card offers
app.get("/api/credit-card-offers", (req, res) => {
	const { bank, category, search } = req.query;
	let filteredOffers = [...creditCardOffers];

	// Filter by bank if specified
	if (bank) {
		const bankList = bank.split(",").map((b) => b.trim().toLowerCase());
		filteredOffers = filteredOffers.filter((offer) => bankList.includes(offer.bank.toLowerCase()));
	}

	// Filter by category if specified
	if (category) {
		const categoryList = category.split(",").map((c) => c.trim().toLowerCase());
		filteredOffers = filteredOffers.filter((offer) => categoryList.includes(offer.category.toLowerCase()));
	}

	// Filter by search term if specified
	if (search) {
		const searchLower = search.toLowerCase();
		filteredOffers = filteredOffers.filter(
			(offer) =>
				offer.bank.toLowerCase().includes(searchLower) ||
				offer.merchant.toLowerCase().includes(searchLower) ||
				offer.description.toLowerCase().includes(searchLower) ||
				offer.category.toLowerCase().includes(searchLower) ||
				offer.discount.toLowerCase().includes(searchLower),
		);
	}

	res.json(filteredOffers);
});

// API endpoint to get all available banks
app.get("/api/banks", (req, res) => {
	const banks = [...new Set(creditCardOffers.map((offer) => offer.bank))];
	res.json(banks);
});

// API endpoint to get all available categories
app.get("/api/categories", (req, res) => {
	const uniqueCategories = [...new Set(creditCardOffers.map((offer) => offer.category))];
	res.json(uniqueCategories);
});

// API endpoint to get a specific credit card offer by ID
app.get("/api/credit-card-offers/:id", (req, res) => {
	const id = parseInt(req.params.id);
	const offer = creditCardOffers.find((offer) => offer.id === id);

	if (!offer) {
		return res.status(404).json({ message: "Credit card offer not found" });
	}

	res.json(offer);
});

// Set up the server port
const PORT = process.env.PORT || 3000;

// Start the server
app.listen(PORT, () => {
	console.log(`Server running on port ${PORT}`);
});
